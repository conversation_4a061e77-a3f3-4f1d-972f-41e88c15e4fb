{"name": "audio-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:staging": "tsc -b && vite build --mode staging", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "preview": "vite preview", "commit": "cz"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@tanstack/react-query": "^5.83.0", "antd": "^5.26.6", "axios": "^1.10.0", "eslint-config-prettier": "^10.1.8", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.7.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@redux-devtools/extension": "^3.3.0", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@unocss/transformer-variant-group": "^66.3.3", "@vitejs/plugin-react": "^4.6.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "unocss": "^66.3.3", "vite": "^7.0.4"}}