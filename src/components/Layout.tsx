import React, { useEffect, useMemo } from 'react';
import { Layout as AntLayout, Button, Avatar, Dropdown } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import {
  SoundOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import SelectLanguage from '@/components/SelectLanguage';
import { useAuthStore } from '@/store/authStore';

const { Header, Content, Footer, Sider } = AntLayout;

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user, isAuthenticated, permissions } = useAuthStore();
  // console.log('isAuthenticated----', isAuthenticated); // false
  // console.log('permissions----', permissions); // []
  // console.log('user----', user); // 和后台返回数据一致的user对象

  // 初始化权限
  useEffect(() => {
    useAuthStore.getState().initializeAuth();
  }, []);
  // 左侧菜单
  const leftMenuItems = useMemo(
    () =>
      permissions.map((item, index) => {
        return (
          <div
            key={item.code || index}
            className="cursor-pointer rounded p-2 hover:bg-gray-100"
            onClick={() => navigate(item.url)}
          >
            {item.name}
          </div>
        );
      }),
    [permissions, navigate]
  );
  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('common.navigation.profile'),
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('common.navigation.settings'),
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('common.navigation.logout'),
      onClick: useAuthStore.getState().logout,
    },
  ];

  const handleLogin = () => {
    navigate('/login');
  };

  const handleRegister = () => {
    navigate('/register');
  };
  const siderStyle: React.CSSProperties = {
    textAlign: 'center',
    lineHeight: '120px',
    color: 'black',
    backgroundColor: 'white',
  };
  return (
    <AntLayout className="min-h-screen">
      <Header className="flex items-center justify-between !bg-white shadow-sm">
        <div className="flex items-center">
          <SoundOutlined className="mr-3 text-2xl text-blue-500" />
          <span className="text-xl font-bold text-gray-800">
            {t('common.appName')}
          </span>
        </div>

        <div className="flex items-center space-x-4">
          {/* 语言切换下拉框 */}
          <SelectLanguage />
          {isAuthenticated ? (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="flex cursor-pointer items-center">
                <Avatar
                  src={user?.avatar}
                  icon={!user?.avatar && <UserOutlined />}
                  className="mr-2"
                />
                <span className="text-gray-700">{user?.alias}</span>
              </div>
            </Dropdown>
          ) : (
            <div className="space-x-2">
              <Button onClick={handleLogin}>
                {t('common.navigation.login')}
              </Button>
              <Button type="primary" onClick={handleRegister}>
                {t('common.navigation.register')}
              </Button>
            </div>
          )}
        </div>
      </Header>
      <AntLayout>
        <Sider width="25%" style={siderStyle}>
          {leftMenuItems}
        </Sider>
        <Content className="flex-1">
          <Outlet />
        </Content>
      </AntLayout>

      <Footer className="border-t bg-gray-50 text-center">
        <p className="text-gray-600">
          {t('common.footer.copyright', { year: new Date().getFullYear() })}
        </p>
      </Footer>
    </AntLayout>
  );
};

export default Layout;
