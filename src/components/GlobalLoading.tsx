import React from 'react';
import { useLoading } from '@/hooks/useApiData';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

/**
 * 全局Loading组件
 * 基于API请求队列自动显示/隐藏loading状态
 */
export const GlobalLoading: React.FC = () => {
  const { isLoading, loadingText } = useLoading();

  if (!isLoading) return null;
  return (
    <div className="fixed left-0 top-0 z-50 flex h-screen w-screen flex-col items-center justify-center gap-3 bg-white/80 pl-10">
      <Spin size="large" indicator={<LoadingOutlined spin />} />
      <div className="color-#1677ff ml-2">{loadingText}</div>
    </div>
  );
};

export default GlobalLoading;
