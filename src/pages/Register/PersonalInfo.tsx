import React from 'react';
import { Card, Form, Input, Button, Typography, message, Row, Col } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterContext } from './index';

const { Title } = Typography;

const PersonalInfo: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterContext();
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    updateFormData(values);
    setCurrentStep(4);
    message.success('个人信息已保存');
    navigate('/register/password');
  };

  const onPrevious = () => {
    setCurrentStep(2);
    navigate('/register/alias');
  };

  // 初始化表单值
  React.useEffect(() => {
    const initialValues = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone,
      address: formData.address,
      country: formData.country,
      state: formData.state,
      postalCode: formData.postalCode,
    };
    form.setFieldsValue(initialValues);
  }, [formData, form]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning('请先完成邮箱验证');
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning('请先设置别名');
      navigate('/register/alias');
    }
  }, [formData.email, formData.alias, navigate]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-md">
        <div className="mb-6 text-center">
          <Title level={2}>{t('auth.register.step3.title')}</Title>
          <p className="mt-2 text-gray-600">
            {t('auth.register.step3.subtitle')}
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t('auth.register.step3.form.firstName')}
                name="firstName"
                rules={[
                  {
                    required: true,
                    message: t('auth.register.step3.form.firstNameRequired'),
                  },
                ]}
              >
                <Input
                  placeholder={t(
                    'auth.register.step3.form.firstNamePlaceholder'
                  )}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t('auth.register.step3.form.lastName')}
                name="lastName"
                rules={[
                  {
                    required: true,
                    message: t('auth.register.step3.form.lastNameRequired'),
                  },
                ]}
              >
                <Input
                  placeholder={t(
                    'auth.register.step3.form.lastNamePlaceholder'
                  )}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label={t('auth.register.step3.form.phone')} name="phone">
            <Input
              placeholder={t('auth.register.step3.form.phonePlaceholder')}
              size="large"
            />
          </Form.Item>

          <Form.Item
            label={t('auth.register.step3.form.address')}
            name="address"
          >
            <Input
              placeholder={t('auth.register.step3.form.addressPlaceholder')}
              size="large"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t('auth.register.step3.form.country')}
                name="country"
              >
                <Input
                  placeholder={t('auth.register.step3.form.countryPlaceholder')}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t('auth.register.step3.form.state')}
                name="state"
              >
                <Input
                  placeholder={t('auth.register.step3.form.statePlaceholder')}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label={t('auth.register.step3.form.postalCode')}
            name="postalCode"
          >
            <Input
              placeholder={t('auth.register.step3.form.postalCodePlaceholder')}
              size="large"
            />
          </Form.Item>

          <div className="flex gap-3">
            <Button onClick={onPrevious} size="large" className="flex-1">
              {t('auth.register.step3.buttons.prev')}
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              className="flex-1"
            >
              {t('auth.register.step3.buttons.next')}
            </Button>
          </div>
        </Form>

        <div className="mt-4 text-center">
          <span className="text-gray-600">
            {t('auth.register.form.hasAccount')}{' '}
          </span>
          <Button
            type="link"
            onClick={() => navigate('/login')}
            className="p-0"
          >
            {t('auth.register.form.login')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PersonalInfo;
