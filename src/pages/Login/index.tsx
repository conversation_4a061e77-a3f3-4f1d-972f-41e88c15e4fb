import React, { useState, useEffect } from 'react';
import { Divider, Card, Form, Input, Button, Typography, message } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

import logo from '@/assets/logo.png';
import UserNameInput from './coms/UserNameInput';
import EmailCodeInput from './coms/EmailCodeInput';
import PasswordInput from './coms/PasswordInput';
import type { LoginFormData } from '@/types/login';
import SelectLanguage from '@/components/SelectLanguage';
import { useAuthStore } from '@/store/authStore';
import { useNavigate, useLocation, Link } from 'react-router-dom';

type LoginStep = 'userName' | 'auth';
type AuthMethod = 'email' | 'password';

const Login: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const [currentStep, setCurrentStep] = useState<LoginStep>('userName');
  const [authMethod, setAuthMethod] = useState<AuthMethod>('password');
  const [form] = Form.useForm<LoginFormData>();

  // 使用useState管理完整的表单数据
  const [formData, setFormData] = useState<LoginFormData>({
    email: '<EMAIL>',
    password: 'password123',
    emailCode: '',
  });

  // 用户名提交
  const handleUsernameSubmit = (values: { email: string }) => {
    // 更新formData中的email字段
    const updatedFormData = { ...formData, email: values.email };
    setFormData(updatedFormData);

    setCurrentStep('auth');
  };

  // 密码登录提交
  const handlePasswordSubmit = (values: Pick<LoginFormData, 'password'>) => {
    // 更新formData中的password字段
    const updatedFormData = { ...formData, password: values.password };
    setFormData(updatedFormData);

    // 这里可以调用登录API，使用updatedFormData
    handleLogin(updatedFormData);
  };

  // 邮箱验证码登录提交
  const handleEmailCodeSubmit = (values: Pick<LoginFormData, 'emailCode'>) => {
    // 更新formData中的emailCode字段
    const updatedFormData = { ...formData, emailCode: values.emailCode };
    setFormData(updatedFormData);

    // 调登录API，使用updatedFormData
    handleLogin(updatedFormData);
  };

  const { login } = useAuthStore();
  // 统一的登录处理函数
  const handleLogin = async (loginData: LoginFormData) => {
    try {
      // 调用实际的登录API
      await login(loginData);
      console.log('location----location', location);

      setTimeout(() => {
        const url = location.state?.from?.pathname || '/';
        navigate(url, { replace: true });
        console.log('登录成功跳转到', url);
      }, 1000);

      message.success('登录成功');
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试');
    }
  };

  // 切换到邮箱验证码登录
  const handleSwitchToEmailCode = () => {
    setAuthMethod('email');
  };

  // 切换到密码登录
  const handleSwitchToPassword = () => {
    setAuthMethod('password');
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'userName':
        return (
          <UserNameInput
            form={form}
            onSubmit={handleUsernameSubmit}
            initialEmail={formData.email} // 传递初始值以支持返回编辑
          />
        );
      case 'auth':
        return authMethod === 'email' ? (
          <EmailCodeInput
            form={form}
            onSubmit={handleEmailCodeSubmit}
            onSwitchToPassword={handleSwitchToPassword}
            userEmail={formData.email}
          />
        ) : (
          <PasswordInput
            form={form}
            onSubmit={handlePasswordSubmit}
            onSwitchToEmailCode={handleSwitchToEmailCode}
            initialPassword={formData.password}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <SelectLanguage className="fixed right-10 top-10" />
      <Card className="w-750px rounded-lg bg-white px-40 shadow-md">
        <div className="">
          <div className="flex justify-center">
            <img src={logo} alt="logo" className="h-40 w-40" />
          </div>
          <div className="leading-20 text-center text-2xl font-bold">
            {t('auth.login.title')}
          </div>
          <Divider className="m-0" />

          <Form
            form={form}
            layout="vertical"
            onValuesChange={(changedValues, allValues) => {
              // console.log('Form values changed:', changedValues, allValues);
            }}
          >
            <div className="h-62 pt-2">{renderStepContent()}</div>
          </Form>
          <Divider className="mb-6 mt-0" />
          <div className="text-center">
            {t('auth.login.form.noAccount')}
            <Link to="/register" className="ml-4 text-blue-500">
              {t('auth.login.form.register')}
            </Link>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Login;
