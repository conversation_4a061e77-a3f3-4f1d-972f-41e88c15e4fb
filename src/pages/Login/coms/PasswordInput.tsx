import React, { useEffect } from 'react';
import { Form, Input, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types/login';

interface PasswordInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'password'>) => void;
  onSwitchToEmailCode: () => void;
  initialPassword?: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  form,
  onSubmit,
  onSwitchToEmailCode,
  initialPassword = '',
}) => {
  useEffect(() => {
    if (initialPassword) {
      form.setFieldsValue({ password: initialPassword });
    }
  }, [initialPassword, form]);

  const { t } = useLanguage();

  const handleSubmit = () => {
    form
      .validateFields(['password'])
      .then(values => {
        onSubmit(values);
      })
      .catch(errorInfo => {
        console.log('Password validation failed:', errorInfo);
      });
  };

  return (
    <>
      <Form.Item
        label={
          <span className="leading-10">{t('auth.login.form.password')}</span>
        }
        name="password"
        messageVariables={{ label: t('auth.login.form.password') }}
        rules={[{ required: true }]}
      >
        <Input.Password
          placeholder={t('auth.register.step4.form.passwordPlaceholder')}
          size="large"
        />
      </Form.Item>
      <div className="h-10 text-center leading-7"></div>
      <Button
        type="primary"
        shape="round"
        size="large"
        className="my-0 w-full"
        onClick={handleSubmit}
      >
        {t('auth.login.form.submit')}
      </Button>

      <div className="h-10 text-center leading-10">
        <Button color="primary" variant="link" onClick={onSwitchToEmailCode}>
          {t('auth.login.form.loginWithEmailCode')}
        </Button>
      </div>
    </>
  );
};

export default PasswordInput;
