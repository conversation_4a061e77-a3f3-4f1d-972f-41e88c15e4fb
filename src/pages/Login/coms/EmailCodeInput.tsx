import React from 'react';
import { Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types/login';

interface EmailCodeInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'emailCode'>) => void;
  onSwitchToPassword: () => void;
  userEmail: string; // 添加用户邮箱显示
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  form,
  onSubmit,
  onSwitchToPassword,
  userEmail,
}) => {
  const { t } = useTranslation();
  const handleSubmit = () => {
    if (form && onSubmit) {
      form
        .validateFields(['emailCode'])
        .then(values => {
          onSubmit(values);
        })
        .catch(errorInfo => {
          console.log('Email code validation failed:', errorInfo);
        });
    }
  };

  // 重新发送验证码
  const handleResendCode = () => {
    console.log('重新发送验证码到邮箱:', userEmail);
    // 这里可以调用发送验证码API，使用formData.email
  };

  return (
    <>
      <Form.Item
        label={
          <span className="leading-10">
            {t('auth.register.step1.form.email')}
          </span>
        }
        name="emailCode"
        rules={[
          { required: true, message: t('common.form.required') },
          { len: 6, message: t('auth.register.step1.form.emailInvalid') },
        ]}
        validateTrigger={['onChange', 'onBlur']}
      >
        <Input
          placeholder={t('auth.register.step4.form.passwordPlaceholder')}
          size="large"
        />
      </Form.Item>

      <div className="h-10 text-center leading-7">
        <Button color="primary" variant="link" onClick={handleResendCode}>
          {t('auth.login.form.resend')}
        </Button>
      </div>

      <Button
        type="primary"
        shape="round"
        size="large"
        className="my-0 w-full"
        onClick={handleSubmit}
      >
        {t('auth.login.form.submit')}
      </Button>

      <div className="h-10 text-center leading-10">
        <Button color="primary" variant="link" onClick={onSwitchToPassword}>
          {t('auth.login.form.loginWithPassword')}
        </Button>
      </div>
    </>
  );
};

export default EmailCodeInput;
